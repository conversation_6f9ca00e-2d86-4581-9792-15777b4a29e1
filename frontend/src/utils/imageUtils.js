/**
 * Image utility functions for handling image URLs and processing
 */

/**
 * Helper function to ensure image URLs are absolute
 * @param {string} imageUrl - The image URL to process
 * @returns {string|null} - The absolute image URL or null if no URL provided
 */
export const getAbsoluteImageUrl = (imageUrl) => {
  if (!imageUrl) return null;

  // If it's already an absolute URL, return it as is
  if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
    return imageUrl;
  }

  // If it's a blob URL, return it as is (let the component handle errors)
  if (imageUrl.startsWith('blob:')) {
    return imageUrl;
  }

  // If it's a relative URL starting with /uploads, make it absolute
  if (imageUrl.startsWith('/uploads')) {
    // Use the current host with the correct port
    const host = window.location.hostname;
    const port = window.location.port || '86'; // Default to port 86 if not specified
    return `http://${host}:${port}${imageUrl}`;
  }

  // Return the original URL if it doesn't match any of the above conditions
  return imageUrl;
};

/**
 * Convert a blob to a data URL
 * @param {Blob} blob - The blob to convert
 * @returns {Promise<string>} - Promise that resolves to the data URL
 */
export const blobToDataUrl = (blob) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result);
    reader.onerror = reject;
    reader.readAsDataURL(blob);
  });
};

/**
 * Convert a data URL to a blob
 * @param {string} dataUrl - The data URL to convert
 * @returns {Blob} - The resulting blob
 */
export const dataUrlToBlob = (dataUrl) => {
  const arr = dataUrl.split(',');
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);

  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }

  return new Blob([u8arr], { type: mime });
};

/**
 * Resize an image to fit within specified dimensions while maintaining aspect ratio
 * @param {HTMLImageElement} img - The image element
 * @param {number} maxWidth - Maximum width
 * @param {number} maxHeight - Maximum height
 * @returns {Object} - Object with width and height properties
 */
export const calculateImageDimensions = (img, maxWidth, maxHeight) => {
  let { width, height } = img;

  // Calculate the scaling factor
  const scaleX = maxWidth / width;
  const scaleY = maxHeight / height;
  const scale = Math.min(scaleX, scaleY);

  // Apply the scale if the image is larger than the max dimensions
  if (scale < 1) {
    width *= scale;
    height *= scale;
  }

  return { width: Math.round(width), height: Math.round(height) };
};

/**
 * Create a canvas element with the specified dimensions
 * @param {number} width - Canvas width
 * @param {number} height - Canvas height
 * @returns {HTMLCanvasElement} - The created canvas element
 */
export const createCanvas = (width, height) => {
  const canvas = document.createElement('canvas');
  canvas.width = width;
  canvas.height = height;
  return canvas;
};

/**
 * Load an image from a URL
 * @param {string} url - The image URL
 * @returns {Promise<HTMLImageElement>} - Promise that resolves to the loaded image
 */
export const loadImage = (url) => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve(img);
    img.onerror = reject;
    img.crossOrigin = 'anonymous'; // Enable CORS for external images
    img.src = url;
  });
};

/**
 * Compress an image to a specified quality
 * @param {HTMLImageElement} img - The image to compress
 * @param {number} quality - Quality factor (0-1)
 * @param {string} format - Output format ('image/jpeg' or 'image/png')
 * @returns {Promise<Blob>} - Promise that resolves to the compressed image blob
 */
export const compressImage = (img, quality = 0.8, format = 'image/jpeg') => {
  return new Promise((resolve) => {
    const canvas = createCanvas(img.width, img.height);
    const ctx = canvas.getContext('2d');

    ctx.drawImage(img, 0, 0);

    canvas.toBlob(resolve, format, quality);
  });
};

/**
 * Get the file extension from a filename or URL
 * @param {string} filename - The filename or URL
 * @returns {string} - The file extension (without the dot)
 */
export const getFileExtension = (filename) => {
  if (!filename) return '';
  const lastDot = filename.lastIndexOf('.');
  return lastDot !== -1 ? filename.substring(lastDot + 1).toLowerCase() : '';
};

/**
 * Check if a file is an image based on its extension
 * @param {string} filename - The filename to check
 * @returns {boolean} - True if the file is an image
 */
export const isImageFile = (filename) => {
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];
  const extension = getFileExtension(filename);
  return imageExtensions.includes(extension);
};


