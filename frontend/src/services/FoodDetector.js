// TensorFlow.js and MobileNet are loaded globally in index.html
// This ensures they're available before the app tries to use them
let tf, mobilenet;

// Function to get TensorFlow.js and MobileNet instances
function getTensorflowInstances() {
  try {
    tf = window.tf;
    mobilenet = window.mobilenet;

    if (tf && mobilenet) {
      console.log('Successfully accessed global TensorFlow.js and MobileNet instances');
      return true;
    }
  } catch (error) {
    console.warn('Could not access global TensorFlow.js or MobileNet instances:', error);
  }
  return false;
}

// Try to get the global instances first
getTensorflowInstances();

// If global instances aren't available, set up a retry mechanism
if (!tf || !mobilenet) {
  console.log('TensorFlow.js or MobileNet not available yet, setting up retry mechanism');

  // Retry every 500ms for up to 10 seconds
  let retryCount = 0;
  const maxRetries = 20;
  const retryInterval = setInterval(() => {
    retryCount++;

    if (getTensorflowInstances()) {
      console.log(`Successfully loaded TensorFlow.js and MobileNet after ${retryCount} retries`);
      clearInterval(retryInterval);
      return;
    }

    if (retryCount >= maxRetries) {
      console.error('Failed to load TensorFlow.js and MobileNet after maximum retries');
      clearInterval(retryInterval);

      // Try dynamic imports as a last resort
      try {
        import('@tensorflow/tfjs').then(tfModule => {
          tf = tfModule;
          console.log('TensorFlow.js loaded via dynamic import');

          import('@tensorflow-models/mobilenet').then(mobilenetModule => {
            mobilenet = mobilenetModule;
            console.log('MobileNet loaded via dynamic import');
          });
        });
      } catch (error) {
        console.error('Failed to load TensorFlow.js and MobileNet via dynamic import:', error);
      }
    }
  }, 500);
}

// Common food-related keywords for dynamic detection
// This uses a more flexible approach to detect food items
const FOOD_KEYWORDS = [
  'food', 'meal', 'dish', 'cuisine', 'edible', 'eat', 'drink', 'beverage',
  'fruit', 'vegetable', 'meat', 'fish', 'bread', 'cake', 'pizza', 'soup',
  'salad', 'sandwich', 'pasta', 'rice', 'cheese', 'milk', 'coffee', 'tea'
];

// Common non-food keywords for dynamic detection
const NON_FOOD_KEYWORDS = [
  'person', 'people', 'human', 'face', 'car', 'vehicle', 'building', 'house',
  'animal', 'pet', 'dog', 'cat', 'bird', 'phone', 'computer', 'screen', 'book',
  'clothing', 'furniture', 'toy', 'plant', 'flower', 'tree', 'sky', 'landscape'
];

class FoodDetector {
  constructor() {
    this.model = null;
    this.isLoading = false;
    this.isModelReady = false;
    this.lastPredictionTime = 0;
    this.predictionInterval = 500; // ms between predictions to avoid performance issues
  }

  /**
   * Load the MobileNet model
   * @returns {Promise<void>}
   */
  async loadModel() {
    if (this.isLoading) {
      return;
    }

    if (this.isModelReady) {
      return;
    }

    try {
      this.isLoading = true;
      console.log('Loading TensorFlow.js MobileNet model...');

      // Check if we have a preloaded model from the global scope
      if (window.tfPreloadedModel) {
        console.log('Using preloaded TensorFlow.js model');
        this.model = window.tfPreloadedModel;
        this.isModelReady = true;
        console.log('Preloaded MobileNet model loaded successfully');
        return;
      }

      // Check if TensorFlow.js and MobileNet are available
      if (!tf || !mobilenet) {
        console.error('TensorFlow.js or MobileNet not available. Food detection will not work.');
        // Set a flag to indicate we should allow capture even without food detection
        window.foodDetectionUnavailable = true;
        return;
      }

      // Ensure TensorFlow.js is ready
      await tf.ready();

      // Load MobileNet model
      this.model = await mobilenet.load({
        version: 2,
        alpha: 0.5 // Use a smaller model for better performance on mobile
      });

      this.isModelReady = true;
      console.log('MobileNet model loaded successfully');
    } catch (error) {
      console.error('Error loading MobileNet model:', error);
      // Set a flag to indicate we should allow capture even without food detection
      window.foodDetectionUnavailable = true;
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * Check if the model is ready
   * @returns {boolean}
   */
  isReady() {
    return this.isModelReady;
  }

  /**
   * Classify an image from a video element
   * @param {HTMLVideoElement} videoElement - The video element to classify
   * @param {Object} viewfinderConfig - Optional configuration for the viewfinder area to crop
   * @returns {Promise<{isFoodDetected: boolean, predictions: Array, topPrediction: Object}>}
   */
  async classifyFromVideo(videoElement, viewfinderConfig = null) {
    // If food detection is unavailable, return a default "food detected" response
    if (window.foodDetectionUnavailable) {
      return {
        isFoodDetected: true, // Always return true to enable the capture button
        predictions: [{ className: 'Food (detection unavailable)', probability: 1.0 }],
        topPrediction: { className: 'Food (detection unavailable)', probability: 1.0 }
      };
    }

    if (!this.isModelReady) {
      console.warn('Model is not ready. Waiting for model to load...');
      return null;
    }

    // Skip prediction if we're predicting too frequently
    const now = Date.now();
    if (now - this.lastPredictionTime < this.predictionInterval) {
      return null;
    }
    this.lastPredictionTime = now;

    try {
      // Make sure the video is playing and has valid dimensions
      if (
        videoElement.readyState === 4 &&
        videoElement.videoWidth > 0 &&
        videoElement.videoHeight > 0
      ) {
        // Use try-catch specifically for the classify call
        try {
          let predictions;

          // If viewfinder config is provided, only analyze the cropped area
          if (viewfinderConfig && tf) {
            predictions = await this.classifyCroppedArea(videoElement, viewfinderConfig);
          } else {
            // Classify the entire frame if no viewfinder config is provided
            predictions = await this.model.classify(videoElement, 5); // Explicitly request 5 predictions
          }

          // Check if predictions are valid
          if (!predictions || predictions.length === 0) {
            console.warn('No predictions returned from model');
            return null;
          }

          // Check if any of the top predictions are food-related
          const isFoodDetected = this.checkIfFood(predictions);

          // Log the top prediction for debugging
          console.log(`Top prediction: ${predictions[0].className} (${(predictions[0].probability * 100).toFixed(2)}%)`);

          return {
            isFoodDetected,
            predictions,
            topPrediction: predictions[0]
          };
        } catch (classifyError) {
          console.error('Error during classification:', classifyError);
          return null;
        }
      } else {
        // Log why video isn't ready
        if (videoElement.readyState !== 4) {
          console.log(`Video not ready: readyState = ${videoElement.readyState}`);
        }
        if (videoElement.videoWidth === 0 || videoElement.videoHeight === 0) {
          console.log(`Invalid video dimensions: ${videoElement.videoWidth}x${videoElement.videoHeight}`);
        }
        return null;
      }
    } catch (error) {
      console.error('Error classifying video frame:', error);
      return null;
    }
  }

  /**
   * Classify only the cropped area of a video frame based on viewfinder dimensions
   * @param {HTMLVideoElement} videoElement - The video element to classify
   * @param {Object} viewfinderConfig - Configuration for the viewfinder area to crop
   * @returns {Promise<Array>} - Array of predictions
   */
  async classifyCroppedArea(videoElement, viewfinderConfig) {
    try {
      // Create a temporary canvas to draw the cropped area
      const tempCanvas = document.createElement('canvas');
      const tempCtx = tempCanvas.getContext('2d');

      // Calculate the actual pixel dimensions of the viewfinder
      const videoWidth = videoElement.videoWidth;
      const videoHeight = videoElement.videoHeight;

      // Parse percentage values from viewfinder config
      const viewfinderWidthPercent = parseFloat(viewfinderConfig.width) / 100;

      // Calculate the actual pixel dimensions of the viewfinder
      const viewfinderWidth = videoWidth * viewfinderWidthPercent;

      // For square viewfinders, calculate height based on width to maintain 1:1 aspect ratio
      let viewfinderHeight;
      if (viewfinderConfig.maintainSquare) {
        // Make it square by using the same pixel dimension as width
        viewfinderHeight = viewfinderWidth;
      } else {
        // Use the configured height percentage
        const viewfinderHeightPercent = parseFloat(viewfinderConfig.height) / 100;
        viewfinderHeight = videoHeight * viewfinderHeightPercent;
      }

      // Calculate the position of the viewfinder (centered horizontally, at 25% from the top)
      const viewfinderX = (videoWidth - viewfinderWidth) / 2;
      const viewfinderY = videoHeight * 0.25; // 25% from the top as specified in ScannerViewfinder.jsx

      // Set the canvas size to match the viewfinder dimensions
      tempCanvas.width = viewfinderWidth;
      tempCanvas.height = viewfinderHeight;

      // Draw only the viewfinder area to the canvas
      tempCtx.drawImage(
        videoElement,
        viewfinderX, viewfinderY, viewfinderWidth, viewfinderHeight, // Source rectangle
        0, 0, viewfinderWidth, viewfinderHeight // Destination rectangle
      );

      // Use TensorFlow.js to create a tensor from the canvas
      const tensor = tf.browser.fromPixels(tempCanvas);

      // Use the model to classify the cropped area
      const predictions = await this.model.classify(tempCanvas, 5);

      // Clean up the tensor to prevent memory leaks
      tensor.dispose();

      return predictions;
    } catch (error) {
      console.error('Error classifying cropped area:', error);
      // Fall back to classifying the entire frame
      return await this.model.classify(videoElement, 5);
    }
  }

  /**
   * Check if the predictions contain food items
   * @param {Array} predictions - The predictions from the model
   * @returns {boolean}
   */
  checkIfFood(predictions) {
    if (!predictions || predictions.length === 0) {
      return false;
    }

    // Track the highest food and non-food confidence scores
    let highestFoodScore = 0;
    let highestNonFoodScore = 0;
    let isFood = false;
    let foodItem = '';
    let nonFoodItem = '';

    // Check all predictions to find food and non-food items
    for (let i = 0; i < Math.min(predictions.length, 5); i++) {
      const prediction = predictions[i];
      const className = prediction.className.toLowerCase();
      const probability = prediction.probability;

      // Check for explicit non-food items using flexible keyword matching
      const isNonFood = NON_FOOD_KEYWORDS.some(keyword =>
        className.includes(keyword)
      );

      if (isNonFood && probability > highestNonFoodScore) {
        highestNonFoodScore = probability;
        nonFoodItem = className;
      }

      // Check for food items using flexible keyword matching
      const isFoodItem = FOOD_KEYWORDS.some(keyword =>
        className.includes(keyword)
      );

      if (isFoodItem && probability > highestFoodScore) {
        highestFoodScore = probability;
        foodItem = className;
      }
    }

    // More lenient decision logic to improve real-time detection:
    // 1. If we have any food item with reasonable confidence, it's food
    // 2. If we have both food and non-food items, prefer food unless non-food is very confident
    // 3. If we have a high confidence non-food item and no food items, it's not food

    // Lower the threshold for food detection to make it more sensitive
    if (highestFoodScore > 0.25) {
      isFood = true;
    }

    // If we have both food and non-food, only reject if non-food is very confident
    if (highestFoodScore > 0 && highestNonFoodScore > 0) {
      if (highestNonFoodScore > 0.8 && highestNonFoodScore > highestFoodScore * 2) {
        isFood = false;
      }
    }

    // If we have a very high confidence non-food item and no significant food items
    if (highestNonFoodScore > 0.85 && highestFoodScore < 0.2) {
      isFood = false;
    }

    // Log the decision for debugging with more details
    console.log(`Food detection: foodItem="${foodItem}" (${(highestFoodScore * 100).toFixed(1)}%), nonFoodItem="${nonFoodItem}" (${(highestNonFoodScore * 100).toFixed(1)}%), isFood=${isFood}`);

    return isFood;
  }

  /**
   * Clean up resources
   */
  dispose() {
    try {
      if (this.model) {
        // Check if the model has a dispose method before calling it
        if (typeof this.model.dispose === 'function') {
          console.log('Disposing TensorFlow.js model');
          this.model.dispose();
        } else if (tf && tf.dispose) {
          // Try using TensorFlow's global dispose if available
          console.log('Model does not have dispose method, using TensorFlow global dispose');
          tf.dispose();
        } else {
          console.log('No dispose method available, setting model to null');
        }

        // Reset model reference and state
        this.model = null;
        this.isModelReady = false;
      } else {
        console.log('No model to dispose');
      }
    } catch (err) {
      console.error('Error in dispose method:', err);
      // Still reset the model state even if disposal fails
      this.model = null;
      this.isModelReady = false;
    }
  }
}

// Create a singleton instance
const foodDetector = new FoodDetector();
export default foodDetector;
