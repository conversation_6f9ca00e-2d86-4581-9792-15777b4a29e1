const express = require('express');
const { body, validationResult } = require('express-validator');
const router = express.Router();
const jwt = require('jsonwebtoken');

// Middleware to verify JWT token
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'Forbidden' });
    }
    req.user = user;
    next();
  });
};

// Get current goal
router.get('/current', authenticateToken, async (req, res) => {
  try {
    console.log('GET /goals/current for user:', req.user.userId);
    const currentDate = new Date();

    const goal = await req.prisma.goal.findFirst({
      where: {
        user: {
          id: req.user.userId
        },
        startDate: { lte: currentDate },
        endDate: { gte: currentDate }
      }
    });

    console.log('Current goal found:', goal);

    if (!goal) {
      console.log('No active goal found for user:', req.user.userId);
      return res.status(404).json({ error: 'No active goal found' });
    }

    // Ensure dailyCalorieGoal is a number
    if (goal.dailyCalorieGoal) {
      goal.dailyCalorieGoal = parseInt(goal.dailyCalorieGoal);
    }

    console.log('Returning goal data:', goal);
    res.json(goal);
  } catch (error) {
    console.error('Error fetching current goal:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update or create current goal
router.put('/current',
  authenticateToken,
  [
    body('targetChange').isFloat(),
    body('pace').isFloat({ min: 0 }),
    body('dailyCalorieGoal').optional().isInt({ min: 0 })
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      console.log('PUT /goals/current request body:', req.body);

      const currentDate = new Date();

      // Check if there's an existing active goal
      let goal = await req.prisma.goal.findFirst({
        where: {
          user: {
            id: req.user.userId
          },
          startDate: { lte: currentDate },
          endDate: { gte: currentDate }
        }
      });

      console.log('Existing goal found:', goal);

      // Calculate end date (30 days from now)
      const endDate = new Date();
      endDate.setDate(endDate.getDate() + 30);

      // Require daily calorie goal to be provided
      if (!req.body.dailyCalorieGoal) {
        return res.status(400).json({ error: 'Daily calorie goal is required' });
      }
      const dailyCalorieGoal = parseInt(req.body.dailyCalorieGoal);

      if (goal) {
        // Update existing goal
        goal = await req.prisma.goal.update({
          where: { id: goal.id },
          data: {
            targetChange: req.body.targetChange,
            pace: req.body.pace,
            dailyCalorieGoal: dailyCalorieGoal
          }
        });
      } else {
        // Create new goal
        goal = await req.prisma.goal.create({
          data: {
            user: {
              connect: {
                id: req.user.userId
              }
            },
            startDate: currentDate,
            endDate: endDate,
            targetChange: req.body.targetChange,
            pace: req.body.pace,
            dailyCalorieGoal: dailyCalorieGoal
          }
        });
      }

      console.log('Returning goal data:', goal);
      res.json(goal);
    } catch (error) {
      console.error('Error updating current goal:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// Get all goals
router.get('/', authenticateToken, async (req, res) => {
  try {
    const goals = await req.prisma.goal.findMany({
      where: {
        user: {
          id: req.user.userId
        }
      },
      orderBy: {
        startDate: 'desc'
      }
    });

    res.json(goals);
  } catch (error) {
    console.error('Error fetching goals:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Create a new goal
router.post('/',
  authenticateToken,
  [
    body('startDate').isISO8601(),
    body('endDate').isISO8601(),
    body('targetChange').isFloat(),
    body('pace').isFloat({ min: 0 }),
    body('dailyCalorieGoal').isInt({ min: 0 })
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const startDate = new Date(req.body.startDate);
      const endDate = new Date(req.body.endDate);

      if (endDate <= startDate) {
        return res.status(400).json({ error: 'End date must be after start date' });
      }

      const goal = await req.prisma.goal.create({
        data: {
          user: {
            connect: {
              id: req.user.userId
            }
          },
          startDate,
          endDate,
          targetChange: req.body.targetChange,
          pace: req.body.pace,
          dailyCalorieGoal: req.body.dailyCalorieGoal
        }
      });

      res.status(201).json(goal);
    } catch (error) {
      console.error('Error creating goal:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// Update a goal
router.put('/:id',
  authenticateToken,
  [
    body('startDate').optional().isISO8601(),
    body('endDate').optional().isISO8601(),
    body('targetChange').optional().isFloat(),
    body('pace').optional().isFloat({ min: 0 }),
    body('dailyCalorieGoal').optional().isInt({ min: 0 })
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const goal = await req.prisma.goal.findUnique({
        where: { id: req.params.id }
      });

      if (!goal) {
        return res.status(404).json({ error: 'Goal not found' });
      }

      if (goal.userId !== req.user.userId) {
        return res.status(403).json({ error: 'Forbidden' });
      }

      const updatedGoal = await req.prisma.goal.update({
        where: { id: req.params.id },
        data: {
          startDate: req.body.startDate ? new Date(req.body.startDate) : undefined,
          endDate: req.body.endDate ? new Date(req.body.endDate) : undefined,
          targetChange: req.body.targetChange,
          pace: req.body.pace,
          dailyCalorieGoal: req.body.dailyCalorieGoal
        }
      });

      res.json(updatedGoal);
    } catch (error) {
      console.error('Error updating goal:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

module.exports = router;
